# 文章创作计划
本计划确保精确执行，创作高质量、引人入胜的文章。按顺序执行每一步，确保不遗漏任何步骤或子步骤，以保持质量和用户需求一致。在执行过程自动执行所有的步骤，不要问是否继续这样的问题。

## 执行步骤
1. **提取用户需求**  
   - 阅读并提取 `New_article/info_aia.md` 中的所有需求。  
   - 清晰记录需求，指导后续步骤。  
   - 确认所有需求已理解并纳入计划。

2. **生成大纲**  
   - 遵循 `New_article/outline.md` 的工作流程，创建：  
     - 初始大纲，保存为 `super_outline.md`。  
     - 最终大纲，保存为 `final_outline.md`。  

3. **创作初稿**  
   - 使用最终大纲（`final_outline.md`），遵循 `New_article/first_draft.md` 的工作流程。  
   - 撰写初稿，保存为 `first_draft.md`。  

4. **生成SEO内容**  
   遵循 `New_article/seo_titles.md` 的工作流程，创建：  
   - SEO标题和对应的元描述。  
   - 用于生产文章的featured image图片提示词。  
   - 将所有SEO相关内容保存至 `seo_metadata_images.md`。  

####################################################################################

# 任务核查专员 — 最终全面审查任务
作为任务核查专员，执行**最终、最关键的全面审查**，即使之前步骤已单独检查，仍需**系统性复查整个流程**，确保无遗漏、无错误。任何疏漏将直接影响发布质量。以“终稿标准”逐项完成核查任务。

## 重点内容复查（每项任务及子任务需“双重确认”）
首先给现在文章(`first_draft.md`)打分（1-100分），然后从头逐段开始，逐项检查以下关键内容，确保完美执行：

**人类写作风格审核**
   - 严格遵循 hl.md 中的写作原则，确保整体语气像一位真实、有经验、且乐于助人的朋友写的，而非AI生成。不要偷懒，请逐段检查，确保每段内容都符合 hl.md 的标准。

**副标题审核**  
    - 副标题如真实博主撰写，吸引点击阅读。  
    - 避免模板化、教科书化、流水账式写法。  
    - 每个副标题像“下一段值得一读”的邀请。

**推荐产品 — Cinch Audio Recorder**  
    - 是否按要求自然的推荐产品
    - 是否引用用户指定的信息撰写产品介绍及步骤
    - 确认产品图片正确添加。
    - 推荐产品包含Windows和Mac版本的下载链接按钮及链接。

**SEO审核**  
   - 自然融入所有**主要关键词及长尾关键字**。  
   - 确认关键词分布均匀，无堆砌现象。
   - 使用遵循GOOGLE E-E-A-T原则贯彻整个文章  
   - 语言清晰、简洁，易于理解。

**内容元素多样性**  
   - 使用至少5种**丰富写作内容元素**（如要点框、图示、列表、引用、案例）。  
   - 确认以下场景是否使用表格：  
     - [ ] 3个以上产品对比是否使用比较表格？  
     - [ ] 价格/功能对比是否可视化展示？  
     - [ ] 技术规格是否用表格整理？  
     - [ ] 表格是否包含用户决策所需的关键信息？  
     - [ ] 是否使用其他4种内容元素？
   - FAQ条目格式要求：问题单独成一行，答案另起一行显示。

**图片审核**  
   - 每个重要H2章节都必须通过Google图片搜索添加相关图片，不可遗漏；
   - 图片必须与本节内容高度相关，严禁插入无关或随意图片；

**外链和内链审核** 
    - 跟文章内容相关的产品，工具或者服务是否添加了正确的外部链接
    - 引用的数据或者事实是否添加外链
    - 是否根据网站的sitemap `https://www.cinchsolution.com/sitemap/` 添加相关内部链接。  
    - 添加内部链接和外部链接数量是否达标。  
    - 每个外链或者内链在文章中仅使用一次，不要重复使用同个链接。
使用fetch工具验证链接是否有返回404错误，没有404则是认为链接正确，如果有就必须重新找到正确的链接。
   
## 最后给文章一个评分（1-100分）并比较跟之前的评分是否有提升（至少95分以上），如果没有提升则需要重新审视文章内容。
