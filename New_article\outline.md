# 第1部分：文章大纲生成

## 执行文章撰写计划
首先，你需要制定一个详细的执行计划。根据用户要求`C:\Cursor_Project\New_article\info_aia.md`的文章主题(Article Topic)创建同名文件夹`C:\Cursor_Project\{keyword}`下创建名为`plan.md`的计划文件，该文件应包含以下内容：
    1. 用户的具体需求和目标
    2. 需要执行的所有步骤的详细清单
    3. 每个步骤的完成标准和检查点
    4. 预期输出的文件清单

## 步骤1：基础的"超级大纲"生成
超级大纲执行步骤如下：

1. 从`info_aia.md`文件中提取所有参考URL的H2至H4级标题。
2. 合并整理提取的标题
   - 把类似标题合并（例如，"DRM如何工作"→"DRM基础"），并用*标记源数量
   - 将整理后的标题按照层级结构（H1→H2→H3→H4）重新组织，形成文章的初步框架大纲（**不包含引言和结论章节**）。
将合并后的初级大纲保存至`/{keyword}/super_outline.md`。

## 步骤2：创建最终的文章大纲
将优化后的文章大纲作为最终版本保存至`/{keyword}/final_outline.md`。

优化大纲步骤如下：
**整合并分析输入：** 根据`/{keyword}/super_outline.md`初级大纲和用户要求(New_article\info_aia.md)。

## 信息研究指令【确保内容优于已有文章】

**目标：**  
在撰写前，通过深入调研，找出当前热门内容的不足与空白，确保文章信息具有独特性、实用性和深度，优于已有内容。

### 执行任务：

1. **启用深度搜索模式，调研最新资源，重点覆盖：**  
   - Google 搜索结果前 10 名文章（内容结构、案例、数据点）  
   - 行业权威报告与新闻资讯（确保信息更新至当前年份）  
   - 社交平台（如 X）、论坛（Reddit、Quora）中的高赞讨论  

2. **查找以下信息空白：**  
   - 当前文章未提及或轻描淡写的 **用户痛点、常见挫折或误区**  
   - 读者对该主题的 **普遍误解** 或理解上的难点  
   - 初学者经常遇到但主流教程未解决的 **失败尝试**  
   - 专家很少公开讨论的 **不成文规则** 或 “我希望早知道的事”  

3. **补全初级大纲中的缺失点：**  
   - 根据调研内容反向对照初步写作大纲，识别遗漏主题并补充  
   - 提供比竞争文章更具体的解决方案、真实案例或操作细节  


4. 整个大纲结构应优先解答用户的核心问题和需求，然后是高级应该和优化等其他方面的标题。

5. 在大纲最后附加在研究过程中发现的**SEO NLP和长尾关键词**综合列表。这些应包括主要关键词和语义变体。

**Introduction、Conclusion和FAQS：**
根据要求完成这3个部分：
- **引言**（100至250字）：
根据用户在info_aia.md中选择的开头策略(A/B/C/D)来撰写文章引言。每种策略的具体要求如下：

  **Opening Strategy Options:**
  **A. Surprising Statistic/Fact Opening:**
  - Use when you discovered significant statistics or industry data during research
  - Best for: Articles about widespread problems, market trends, or surprising industry insights
  - Use current, verifiable statistics from your research (2024-2025 data preferred)

  **B. Rhetorical Question Opening:**
  - Use when your research revealed strong user frustrations or common complaints
  - Best for: Articles addressing widely discussed problems in forums
  - Base the question on actual user frustrations or common problems found in your research

  **C. "What if" Scenario Opening:**
  - Use when your article provides a clear transformation or significant improvement
  - Best for: Tutorial articles that promise specific outcomes
  - Present a solution-focused scenario based on the benefits your article delivers

  **D. Personal Experience/Case Study Opening:**
  - Use when you discovered interesting case studies or user experiences during research
  - Best for: Articles where real user experiences illustrate the problem/solution
  - Reference real scenarios from forums, reviews, or expert testimonials you discovered

  **E. Relatable Scenario Opening:**
  - Best for: Articles about common, everyday situations users face
  - Create a specific, realistic scenario that your target audience frequently encounters

- **结论**（100至250字）：
  总结要点，强调价值主张（例如，"现在你可以随时随地欣赏音乐"），并包含号召性用语（例如，"在评论中分享你的技巧"）。

- **FAQS**（100至200字）：
  保持3-5个常见问题，每个问题都以一句话简明回答。问题和答案分成2行，不要放在一行。问题和答案之间用空行隔开。

## **优先推荐解决方案**：
遵循`New_article/info_aia.md`中用户指定的产品推荐指南，确保适当的产品放置和推广。

## **文章整体字数控制**：【必要】
1. **自动读取字数范围**：从`New_article/info_aia.md`中提取文章总字数范围，并计算中位数。

2. **智能字数分配算法**：
   根据总字数自动分配各章节字数，遵循以下比例：
   - Introduction: 8-10%
   - 核心推荐章节(推荐产品): 20-25%
   - 主要对比章节(工具对比/方法介绍): 35-40%
   - 支撑章节(背景说明/故障排除/最佳实践): 25-30%
   - Conclusion + FAQ: 8-12%

3. **字数分配验证**：
   - 计算所有章节字数分配的总和
   - 确保总和在目标字数范围内(不超过上限的95%)
   - 如超出，按重要性优先级自动调整各章节分配

4. **标注格式**：
   在每个H2标题后标注：`(目标字数: XXX-XXX words)`
   在每个H3标题后标注：`(目标字数: XXX words)`

5. **通用字数分配公式**：
   设目标字数为 T (从info_aia.md读取范围的中位数)
   - Introduction: T × 8-10%
   - 核心推荐章节: T × 20-25%
   - 主要内容章节: T × 35-40%
   - 支撑章节: T × 25-30%
   - Conclusion + FAQ: T × 8-12%

6. **自动调整机制**：
   如分配总和超出目标上限95%，按以下优先级缩减：
   1. 支撑章节 (-10%)
   2. 主要内容章节 (-5%)
   3. 核心推荐章节 (保持不变)

7. **执行检查**：
   生成大纲后，必须验证：
   - [ ] 所有章节字数分配总和是否在目标范围内
   - [ ] 核心推荐产品章节是否获得足够字数分配(20-25%)
   - [ ] 是否为每个标题标注了具体字数目标
   - [ ] 字数分配是否合理，避免后续需要大幅调整

**字数分配示例**：
假设从info_aia.md读取到目标为"1,600-2,000 words"，取中位数1,800词：
- Introduction: 144-180 words (8-10%)
- 背景说明章节: 162-198 words (9-11%)
- 工具对比章节: 324-360 words (18-20%)
- 在线工具章节: 216-252 words (12-14%)
- 核心推荐产品章节: 360-450 words (20-25%)
- 对比分析章节: 144-180 words (8-10%)
- 故障排除章节: 108-144 words (6-8%)
- 最佳实践章节: 108-144 words (6-8%)
- Conclusion: 72-108 words (4-6%)
- FAQ: 108-144 words (6-8%)
**总计**: 1,646-2,159 words ✅符合目标范围

严格遵守所有章节字数总和不超过用户限制(超过就必须要重新调整章节字数)。在每个标题后标注具体字数目标，便于后续内容创作时参考。
- 各章节分配总和：[计算结果] words
- 状态：✅符合/❌超出 目标范围